trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ coalesce(split(parameters.example, '/')[2], split(parameters.example, '/')[1]) }} • ${{ parameters.action }}

parameters:
- name: environment
  type: string
  default: DEV_gwc_iac
  values:
  - DEV_gwc_iac
  - DEV_gwc_iac-dmz
  - DEV_weu_iac
  - TST_gwc_iac
  - TST_weu_iac
  - PRD_gwc_iac
  - PRD_weu_iac

- name: example
  type: string
  default: examples/01-default
  values:
  - examples/01-default
  - examples/02-encryption
  - examples/03-disabled-acr
  
- name: action
  type: string
  default: plan
  values:
    - plan
    - apply
    - destroy
    - test

- name: skip_checkov
  type: boolean
  default: true

- name: timeout_in_minutes
  type: number
  default: 60

- name: no_proxy
  type: string
  default: '*.core.windows.net'

- name: terraformUnlockStateLockID
  type: string
  default: ' '

- name: pipelinetemplatesTag
  type: string
  default: v8
  values:
    - v7
    - v8

variables:
  - group: 'Centrally managed variable group'
  - template: /env/azure/composer.yaml@tooling
    parameters:
      environment: ${{ split(parameters.environment, '_')[0] }}
      region: ${{ split(parameters.environment, '_')[1] }}
      project: ${{ split(parameters.environment, '_')[2] }}

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: new_env #refs/tags/v6
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/${{ parameters.pipelinetemplatesTag }}
      endpoint: devopsinfra

extends:
  template: iac-pipelines/iac-execute.yaml@tooling
  parameters:
    action: ${{ parameters.action }}
    environment: ${{ split(parameters.environment, '_')[0] }}-${{ split(parameters.environment, '_')[2] }}
    terraformProjectLocation: ${{ parameters.example }}
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}
    skipCheckovScan: ${{ parameters.skip_checkov }}
    appCode: ${{ variables.appCode }}
    armServiceConnectionName: ${{ variables.armServiceConnectionName }}
    storageAccountResourceGroup: ${{ variables.storageAccountResourceGroup }}
    storageAccountName: ${{ variables.storageAccountName }}
    storageAccountContainerName: ${{ variables.storageAccountContainerName }}
    # terraformVersion: '1.7.4'
    pipelinetemplatesTag: ${{ parameters.pipelinetemplatesTag }}
