# Step: automatic tfvars generation from template
# All variables are available from composer.yaml, no need to pass them as parameters
parameters:
  - name: terraformProjectLocation

steps:
- task: Bash@3
  displayName: 'Generate tfvars from template'
  inputs:
    targetType: 'inline'
    script: |
      set -e

      PROJECT_DIR="${{ parameters.terraformProjectLocation }}"
      TEMPLATE_FILE="$PROJECT_DIR/terraform.tfvars.template"

      echo "========================================="
      echo "Generating tfvars from template"
      echo "========================================="
      echo ""

      # Check if template file exists
      if [ ! -f "$TEMPLATE_FILE" ]; then
        echo "Template file not found: $TEMPLATE_FILE"
        echo "Using existing tfvars files..."
        exit 0
      fi

      # Check if required variables are defined
      REQUIRED_VARS_MISSING=false

      if [ -z "$(environment)" ] || [ "$(environment)" == "\$(environment)" ]; then
        REQUIRED_VARS_MISSING=true
      fi

      if [ -z "$(region)" ] || [ "$(region)" == "\$(region)" ]; then
        REQUIRED_VARS_MISSING=true
      fi

      if [ -z "$(project)" ] || [ "$(project)" == "\$(project)" ]; then
        REQUIRED_VARS_MISSING=true
      fi

      if [ "$REQUIRED_VARS_MISSING" = true ]; then
        echo "Skipping tfvars generation from template..."
        echo "Using existing tfvars files..."
        exit 0
      fi

      OUTPUT_FILENAME="$(environment)-$(region)-$(project).tfvars"
      OUTPUT_FILE="$PROJECT_DIR/$OUTPUT_FILENAME"

      echo "Files:"
      echo "  Template: $TEMPLATE_FILE"
      echo "  Output:   $OUTPUT_FILE"
      echo ""

      # Convert resourceSuffix to uppercase for sed replacement
      # Use default empty value if not defined
      RESOURCE_SUFFIX_VALUE="$(resourceSuffix)"
      if [ -z "$RESOURCE_SUFFIX_VALUE" ] || [ "$RESOURCE_SUFFIX_VALUE" == "\$(resourceSuffix)" ]; then
        RESOURCE_SUFFIX_UPPER=""
      else
        RESOURCE_SUFFIX_UPPER=$(echo "$RESOURCE_SUFFIX_VALUE" | tr '[:lower:]' '[:upper:]')
      fi

      # Helper function to get variable value or empty string if not defined
      get_var_value() {
        local var_value="$1"
        if [ -z "$var_value" ] || [[ "$var_value" == \$\(* ]]; then
          echo ""
        else
          echo "$var_value"
        fi
      }

      # Collect all variable values with safe defaults
      AKS_SUBNET_NAME=$(get_var_value "$(aks_subnet_name)")
      APP_CODE=$(get_var_value "$(appCode)")
      APPGW_PRIVATE_IP=$(get_var_value "$(appgw_private_ip)")
      APPGW_SUBNET_NAME=$(get_var_value "$(appgw_subnet_name)")
      ARM_SERVICE_CONNECTION_NAME=$(get_var_value "$(armServiceConnectionName)")
      CLOUD=$(get_var_value "$(cloud)")
      COMPUTE_SUBNET_NAME=$(get_var_value "$(compute_subnet_name)")
      ENVIRONMENT=$(get_var_value "$(environment)")
      INGRESS_NGINX_IP=$(get_var_value "$(ingress_nginx_ip)")
      KEY_VAULT_COMMA_SEPARATED_SECRET_NAMES=$(get_var_value "$(keyVaultCommaSeparatedSecretNames)")
      KEY_VAULT_NAME=$(get_var_value "$(key_vault_name)")
      KEY_VAULT_RESOURCE_GROUP_NAME=$(get_var_value "$(key_vault_resource_group_name)")
      KEY_VAULT_SERVICE_CONNECTION_NAME=$(get_var_value "$(keyVaultServiceConnectionName)")
      PRIVATE_ENDPOINT_SUBNET_NAME=$(get_var_value "$(private_endpoint_subnet_name)")
      PROJECT=$(get_var_value "$(project)")
      REGION=$(get_var_value "$(region)")
      REGION_FULL=$(get_var_value "$(region_full)")
      REGION_SHORT=$(get_var_value "$(region)")
      RESOURCE_GROUP_NAME=$(get_var_value "$(storageAccountResourceGroup)")
      ROUTE_TABLE_NAME=$(get_var_value "$(route_table_name)")
      ROUTE_TABLE_RESOURCE_GROUP_NAME=$(get_var_value "$(route_table_resource_group_name)")
      STORAGE_ACCOUNT_CONTAINER_NAME=$(get_var_value "$(storageAccountContainerName)")
      STORAGE_ACCOUNT_NAME=$(get_var_value "$(storageAccountName)")
      STORAGE_ACCOUNT_RESOURCE_GROUP=$(get_var_value "$(storageAccountResourceGroup)")
      SUBSIDIARY=$(get_var_value "$(subsidiary)")
      TARGET=$(get_var_value "$(target)")
      TERRAFORM_VERSION=$(get_var_value "$(terraformVersion)")
      VIRTUAL_NETWORK_NAME=$(get_var_value "$(virtual_network_name)")
      VIRTUAL_NETWORK_RESOURCE_GROUP_NAME=$(get_var_value "$(virtual_network_resource_group_name)")

      sed -e "s/{{ AKS_SUBNET_NAME }}/$AKS_SUBNET_NAME/g" \
          -e "s/{{ APP_CODE }}/$APP_CODE/g" \
          -e "s/{{ APPGW_PRIVATE_IP }}/$APPGW_PRIVATE_IP/g" \
          -e "s/{{ APPGW_SUBNET_NAME }}/$APPGW_SUBNET_NAME/g" \
          -e "s/{{ ARM_SERVICE_CONNECTION_NAME }}/$ARM_SERVICE_CONNECTION_NAME/g" \
          -e "s/{{ CLOUD }}/$CLOUD/g" \
          -e "s/{{ COMPUTE_SUBNET_NAME }}/$COMPUTE_SUBNET_NAME/g" \
          -e "s/{{ ENVIRONMENT }}/$ENVIRONMENT/g" \
          -e "s/{{ INGRESS_NGINX_IP }}/$INGRESS_NGINX_IP/g" \
          -e "s/{{ KEY_VAULT_COMMA_SEPARATED_SECRET_NAMES }}/$KEY_VAULT_COMMA_SEPARATED_SECRET_NAMES/g" \
          -e "s/{{ KEY_VAULT_NAME }}/$KEY_VAULT_NAME/g" \
          -e "s/{{ KEY_VAULT_RESOURCE_GROUP_NAME }}/$KEY_VAULT_RESOURCE_GROUP_NAME/g" \
          -e "s/{{ KEY_VAULT_SERVICE_CONNECTION_NAME }}/$KEY_VAULT_SERVICE_CONNECTION_NAME/g" \
          -e "s/{{ PRIVATE_ENDPOINT_SUBNET_NAME }}/$PRIVATE_ENDPOINT_SUBNET_NAME/g" \
          -e "s/{{ PROJECT }}/$PROJECT/g" \
          -e "s/{{ REGION }}/$REGION/g" \
          -e "s/{{ REGION_FULL }}/$REGION_FULL/g" \
          -e "s/{{ REGION_SHORT }}/$REGION_SHORT/g" \
          -e "s/{{ RESOURCE_GROUP_NAME }}/$RESOURCE_GROUP_NAME/g" \
          -e "s/{{ RESOURCE_SUFFIX }}/$RESOURCE_SUFFIX_UPPER/g" \
          -e "s/{{ ROUTE_TABLE_NAME }}/$ROUTE_TABLE_NAME/g" \
          -e "s/{{ ROUTE_TABLE_RESOURCE_GROUP_NAME }}/$ROUTE_TABLE_RESOURCE_GROUP_NAME/g" \
          -e "s/{{ STORAGE_ACCOUNT_CONTAINER_NAME }}/$STORAGE_ACCOUNT_CONTAINER_NAME/g" \
          -e "s/{{ STORAGE_ACCOUNT_NAME }}/$STORAGE_ACCOUNT_NAME/g" \
          -e "s/{{ STORAGE_ACCOUNT_RESOURCE_GROUP }}/$STORAGE_ACCOUNT_RESOURCE_GROUP/g" \
          -e "s/{{ SUBSIDIARY }}/$SUBSIDIARY/g" \
          -e "s/{{ TARGET }}/$TARGET/g" \
          -e "s/{{ TERRAFORM_VERSION }}/$TERRAFORM_VERSION/g" \
          -e "s/{{ VIRTUAL_NETWORK_NAME }}/$VIRTUAL_NETWORK_NAME/g" \
          -e "s/{{ VIRTUAL_NETWORK_RESOURCE_GROUP_NAME }}/$VIRTUAL_NETWORK_RESOURCE_GROUP_NAME/g" \
          "$TEMPLATE_FILE" > "$OUTPUT_FILE"

      echo ""
      echo "========================================="
      echo "Generated tfvars file content:"
      echo "========================================="
      cat "$OUTPUT_FILE"
      echo "========================================="

      # Export only the filename (not the full path) because the plan step
      # runs in the same working directory (terraformProjectLocation)
      echo "##vso[task.setvariable variable=terraformVarsFile]$OUTPUT_FILENAME"
      echo "Exported terraformVarsFile variable: $OUTPUT_FILENAME"
